/* B+树可视化组件样式 */

/* 主容器 */
.bplus-visualizer {
  width: 100%;
  height: 100vh;
  position: relative;
  background: var(--background);
  color: var(--primary-text);
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
}

/* React Flow 画布容器 */
.bplus-canvas-container {
  flex: 1;
  position: relative;
}

.bplus-visualizer.dark-mode {
  background: var(--background);
  color: var(--primary-text);
}

/* 统计信息面板样式 */
.bplus-stats-panel {
  z-index: 1000;
}

.bplus-stats-panel .MuiPaper-root {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid var(--card-border);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] .bplus-stats-panel .MuiPaper-root {
  background: rgba(42, 42, 42, 0.95);
  border-color: var(--card-border);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 操作面板样式 */
.bplus-operations-panel {
  z-index: 1000;
}

.bplus-operations-panel .MuiPaper-root {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid var(--card-border);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

[data-theme="dark"] .bplus-operations-panel .MuiPaper-root {
  background: rgba(42, 42, 42, 0.95);
  border-color: var(--card-border);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 拖拽手柄样式 */
.bplus-resize-handle {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: #d9d9d9;
  cursor: row-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.bplus-resize-handle:hover {
  background: #bfbfbf;
}

.bplus-resize-handle:active {
  background: #999;
}

.dark-mode .bplus-resize-handle {
  background: #404040;
}

.dark-mode .bplus-resize-handle:hover {
  background: #595959;
}

.dark-mode .bplus-resize-handle:active {
  background: #737373;
}

.bplus-resize-handle-indicator {
  width: 40px;
  height: 2px;
  background: #999;
  border-radius: 1px;
  transition: background-color 0.2s ease;
}

.dark-mode .bplus-resize-handle-indicator {
  background: #d9d9d9;
}

/* 内部节点样式 */
.bplus-internal-node {
  background: #e0f2f7;
  border: 2px solid var(--secondary-text);
  border-radius: 8px;
  padding: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

[data-theme="dark"] .bplus-internal-node {
  background: #37474f;
  border-color: var(--secondary-text);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 叶子节点样式 */
.bplus-leaf-node {
  background: #fbe9e7;
  border: 2px solid var(--secondary-text);
  border-radius: 8px;
  padding: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

[data-theme="dark"] .bplus-leaf-node {
  background: #4e342e;
  border-color: var(--secondary-text);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 节点内容 */
.bplus-node-content {
  position: relative;
}

/* 内部节点布局 */
.bplus-internal-layout {
  display: flex;
  align-items: center;
  gap: 0px;
  justify-content: center;
  position: relative;
  padding: 0px;
}

/* 叶子节点布局 */
.bplus-leaf-layout {
  display: flex;
  flex-direction: column;
  gap: 0px;
  padding: 0px;
}

/* 键行布局 */
.bplus-key-row {
  display: flex;
  gap: 2px;
  justify-content: center;
}

/* 槽位容器 */
.bplus-slot-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

/* 单个槽位样式 */
.bplus-slot {
  position: relative;
  min-width: 60px;
  height: 45px;
  border: 2px solid #999999;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-left: -2px;
}

.bplus-slot:first-child {
  margin-left: 0;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.bplus-slot:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

[data-theme="dark"] .bplus-slot {
  border-color: var(--secondary-text);
  background: var(--card-bg);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 空槽位样式 */
.bplus-slot-empty {
  background: var(--background);
  border-style: dashed;
  opacity: 0.6;
}

[data-theme="dark"] .bplus-slot-empty {
  background: var(--background);
}

/* 槽位内容 */
.bplus-slot-content {
  font-size: 16px;
  font-weight: bold;
  color: var(--primary-text);
  -webkit-user-select: none;
  user-select: none;
}

[data-theme="dark"] .bplus-slot-content {
  color: var(--primary-text);
}

/* Handle样式 */
.bplus-handle {
  width: 4px !important;
  height: 4px !important;
  border: 1px solid #a6b9d9 !important;
  background: #6b87a5 !important;
  border-radius: 50% !important;
}

.bplus-handle-target {
  background: white;
}

.bplus-handle-source {
  background: #6383c3;
}

.bplus-handle-sibling {
  background: #ff9800;
  border-color: #ff9800;
}

/* 信息面板 */
.bplus-info-panel {
  position: absolute;
  top: 10px;
  right: 10px;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  padding: 12px;
  min-width: 200px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

[data-theme="dark"] .bplus-info-panel {
  background: var(--card-bg);
  border-color: var(--card-border);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.bplus-tree-stats h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--primary-text);
}

[data-theme="dark"] .bplus-tree-stats h3 {
  color: var(--primary-text);
}

.bplus-tree-stats p {
  margin: 4px 0;
  font-size: 12px;
  color: var(--secondary-text);
}

[data-theme="dark"] .bplus-tree-stats p {
  color: var(--secondary-text);
}

/* React Flow控件样式 */
.bplus-controls {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 8px;
}

[data-theme="dark"] .bplus-controls {
  background: var(--card-bg);
  border-color: var(--card-border);
}

.bplus-minimap {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 8px;
}

[data-theme="dark"] .bplus-minimap {
  background: var(--card-bg);
  border-color: var(--card-border);
}

.bplus-background {
  background: var(--background);
}

[data-theme="dark"] .bplus-background {
  background: var(--background);
}

/* 动画效果样式 */
.nodeHighlighted {
  animation: nodeHighlight 0.5s ease-in-out;
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.6) !important;
  border: 2px solid #4caf50 !important;
}

/* 节点溢出状态样式 */
.nodeOverflow {
  border: 3px solid #e27a72 !important;
  background: rgba(244, 67, 54, 0.1) !important;
  /* box-shadow: 0 0 20px rgba(244, 67, 54, 0.4) !important; */
  animation: nodeOverflowPulse 1s ease-in-out infinite alternate;
}

.dark-mode .nodeOverflow {
  background: rgba(244, 67, 54, 0.2) !important;
  box-shadow: 0 0 20px rgba(244, 67, 54, 0.6) !important;
}

/* 键高亮样式 */
.bplus-key-highlighted {
  background: #d9dfe3 !important;
  border-color: #69a1bb !important;
  box-shadow: 0 0 10px rgba(59, 151, 209, 0.6) !important;
  animation: keyHighlightPulse 1s ease-in-out infinite alternate;
}

[data-theme="dark"] .bplus-key-highlighted {
  background: rgba(255, 235, 59, 0.3) !important;
  border-color: #ffb74d !important;
  box-shadow: 0 0 10px rgba(255, 183, 77, 0.8) !important;
}

.edgeHighlighted {
  stroke: #4caf50 !important;
  stroke-width: 3px !important;
  animation: edgeHighlight 0.5s ease-in-out;
}

.nodeNew {
  animation: nodeAppear 0.6s ease-out;
}

.nodeRemoving {
  animation: nodeDisappear 0.4s ease-in;
}

.nodeOnPath {
  box-shadow: 0 0 10px 2px #2196f3 !important;
  border: 2.5px solid #2196f3 !important;
  background: #e3f2fd !important;
  z-index: 2;
}

.dark-mode .nodeOnPath {
  box-shadow: 0 0 10px 2px #90caf9 !important;
  border: 2.5px solid #90caf9 !important;
  background: #263238 !important;
}

/* 动画关键帧 */
@keyframes nodeHighlight {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(76, 175, 80, 0);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.8);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.6);
  }
}

@keyframes edgeHighlight {
  0% {
    stroke-width: 1px;
    opacity: 0.7;
  }
  50% {
    stroke-width: 4px;
    opacity: 1;
  }
  100% {
    stroke-width: 3px;
    opacity: 1;
  }
}

@keyframes nodeAppear {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-10px);
  }
  60% {
    opacity: 0.8;
    transform: scale(1.05) translateY(0);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes nodeOverflowPulse {
  0% {
    box-shadow: 0 0 15px rgba(244, 67, 54, 0.4);
    border-color: #f44336;
  }
  100% {
    box-shadow: 0 0 25px rgba(244, 67, 54, 0.8);
    border-color: #d32f2f;
  }
}

/* @keyframes keyHighlightPulse {
  0% {
    box-shadow: 0 0 5px rgba(124, 181, 212, 0.4);
  }
  100% {
    box-shadow: 0 0 15px rgba(133, 187, 210, 0.8);
  }
} */

@keyframes nodeDisappear {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

/* 平滑过渡效果 */
.bplus-internal-node,
.bplus-leaf-node {
  transition: all 0.3s ease-in-out;
}

.react-flow__edge {
  transition:
    stroke 0.3s ease-in-out,
    stroke-width 0.3s ease-in-out;
}

/* 自定义横向排列的操作按钮容器 */
.bplus-custom-controls {
  display: flex;
  gap: 8px;
  padding: 8px;
  background-color: transparent !important;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  position: absolute;
  right: 24px;
  bottom: 24px;
  z-index: 10;
}

.bplus-zoom-button {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  color: var(--secondary-text);
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.6);
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.bplus-zoom-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.85);
}

.bplus-zoom-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.bplus-fit-button {
  font-size: 14px;
}

.bplus-style-button svg,
.bplus-reset-button svg {
  width: 16px;
  height: 16px;
}

/* 节点高亮样式 */
.bplus-node-highlighted {
  animation: bplus-highlight-pulse 1s ease-in-out alternate;
  border: 2px solid #5a80b2 !important;
  border-color: #4587a1;
}

@keyframes bplus-highlight-pulse {
  33% {
    scale: 0.98;
  }
  66% {
    scale: 1.02;
  }
  100% {
    scale: 1;
  }
}

/* 暗色模式下的高亮样式 */
[data-theme="dark"] .bplus-node-highlighted {
  box-shadow: 0 0 15px rgba(255, 235, 59, 0.8) !important;
  border: 2px solid #ffeb3b !important;
  animation: bplus-highlight-pulse-dark 1s ease-in-out infinite alternate;
}

@keyframes bplus-highlight-pulse-dark {
  33% {
    scale: 0.98;
  }
  66% {
    scale: 1.02;
  }
  100% {
    scale: 1;
  }
}

/* 暗色模式适配 */
[data-theme="dark"] .bplus-custom-controls {
  background-color: transparent !important;
}
[data-theme="dark"] .bplus-zoom-button {
  background: rgba(40, 40, 40, 0.7);
  color: var(--primary-text);
}
[data-theme="dark"] .bplus-zoom-button:hover {
  background: rgba(40, 40, 40, 0.95);
}
