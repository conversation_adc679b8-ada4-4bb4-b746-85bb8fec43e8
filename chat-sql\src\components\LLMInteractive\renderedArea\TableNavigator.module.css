.tableListContainer {
  min-width: 200px !important;
  max-width: 300px !important;
  max-height: 300px !important;
  overflow-y: auto;
  overflow-x: hidden;
  border-radius: 12px;
  background: var(--card-bg) !important;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  padding: 8px 0 8px 0;
  color: var(--primary-text);
}

.searchInput {
  padding: 4px 12px;
  width: 190px !important;
  background-color: var(--button-bg);
  border-radius: 15px;
  border: none !important;
  color: var(--primary-text) !important;
  transition: all 0.3s !important;
}

[data-theme="dark"] .searchInput {
  background-color: #504d4d;
}

.tableList {
  padding: 0;
  margin: 0;
}

.tableListItem {
  transition: background 0.2s;
  border-radius: 8px;
  margin: 2px 8px;
}

.tableListItemButton {
  border-radius: 8px;
  padding: 6px 18px;
  min-width: 0;
  justify-content: flex-start;
}

.tableListItemButton:hover {
  background: #f5f7fa;
}

.tableListText {
  font-size: 1.08em;
  font-weight: 500;
  color: var(--secondary-text);
  letter-spacing: 0.01em;
}

.noMatch {
  color: #aaa;
  text-align: center;
  padding: 16px 0;
  font-size: 1em;
}
