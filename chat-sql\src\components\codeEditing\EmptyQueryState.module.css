.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  height: 100%;
  background-color: var(--card-bg) !important;
  border-radius: 8px;
  border: 1px dashed var(--card-border) !important;
  transition: all 0.3s ease;
}

.icon {
  font-size: 6rem !important;
  color: #f0c14b; /* 金黄色，保持不变 */
  opacity: 0.8;
  margin-bottom: 1rem;
  animation: pulse 2s infinite ease-in-out;
}

.title {
  font-weight: 500;
  color: var(--primary-text);
  text-align: center;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: var(--secondary-text);
  text-align: center;
  max-width: 80%;
  font-size: 0.875rem;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}
