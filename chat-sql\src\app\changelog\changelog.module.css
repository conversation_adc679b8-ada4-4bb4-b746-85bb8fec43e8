.container {
  min-height: 100vh;
  margin-top: var(--navbar-height);
  position: relative;
  overflow: hidden;
  background: transparent !important;
}

.parallaxBackground {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("/assets/landscape.jpg");
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  opacity: 0.4;
  z-index: -1;
}

.content {
  position: relative;
  padding: 24px;
  max-width: 1000px;
  margin: 0 auto;
  z-index: 1;
  background: transparent !important;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: transparent !important;
}
