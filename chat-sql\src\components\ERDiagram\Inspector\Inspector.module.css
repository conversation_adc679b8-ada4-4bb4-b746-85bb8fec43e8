.inspectorContainer {
  height: 100%;
  background: var(--card-bg);
  padding: 16px;
  overflow-y: auto;
}

.viewTitle {
  color: var(--primary-text);
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.componentsView,
.entitiesView,
.relationshipsView {
  height: 100%;
}

.componentGrid {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.componentCard {
  cursor: grab;
  transition: all 0.3s ease;
  border: 1px solid var(--card-border);
  -webkit-user-select: none;
  user-select: none;
}

.componentCard:hover {
  border-color: #1677ff;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.15);
  transform: translateY(-1px);
}

.componentCard:active {
  cursor: grabbing;
  transform: translateY(0);
}

.componentCard[draggable="true"]:hover {
  box-shadow: 0 4px 12px rgba(22, 119, 255, 0.25);
}

.componentItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
}

.componentIcon {
  font-size: 20px;
  color: var(--icon-color);
  flex-shrink: 0;
}

.componentInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.componentDescription {
  font-size: 11px !important;
  line-height: 1.3 !important;
}

.entityItem,
.relationshipItem {
  padding: 8px 0 !important;
  border-bottom: 1px solid var(--divider-color);
}

.entityItem:last-child,
.relationshipItem:last-child {
  border-bottom: none;
}

.entityInfo,
.relationshipInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
  width: 100%;
}

.entityDescription,
.relationshipDescription {
  font-size: 11px !important;
  line-height: 1.3 !important;
}

.instructionText {
  margin-top: 16px;
  padding: 12px;
  background: var(--code-bg);
  border-radius: 6px;
  border-left: 3px solid #1677ff;
}

.instructionText p {
  color: var(--secondary-text);
  font-size: 12px;
  margin: 4px 0;
  line-height: 1.4;
}

/* 暗色主题支持 */
[data-theme="dark"] .componentCard {
  background: var(--card-bg);
  border-color: var(--card-border);
}

[data-theme="dark"] .componentCard:hover {
  border-color: #1677ff;
  background: rgba(22, 119, 255, 0.05);
}

[data-theme="dark"] .instructionText {
  background: rgba(255, 255, 255, 0.05);
}

/* 新增的实体和关系卡片样式 */
.entityList,
.relationshipList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.entityCard,
.relationshipCard {
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.2s ease;
}

.entityCard:hover,
.relationshipCard:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.entityCard.selected,
.relationshipCard.selected {
  border-color: var(--primary-color);
  background-color: rgba(24, 144, 255, 0.05);
}

.entityHeader,
.relationshipHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.entityActions,
.relationshipActions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.entityCard:hover .entityActions,
.relationshipCard:hover .relationshipActions {
  opacity: 1;
}

.expandIcon {
  font-size: 12px;
  color: var(--secondary-text);
  transition: transform 0.2s ease;
}

.entityContent,
.relationshipContent {
  padding: 0 12px 12px;
}

.attributesList,
.connectionsList {
  margin-bottom: 8px;
}

.sectionTitle {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 4px;
  display: block;
}

.attributeItem,
.connectionItem {
  padding: 4px 8px;
  margin: 2px 0;
  background: var(--background-light);
  border-radius: 4px;
  border-left: 3px solid var(--primary-color);
}

.attributeInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.attributeType {
  font-size: 11px;
  font-family: monospace;
}

.primaryKeyIcon {
  color: #d32f2f;
  font-size: 14px;
}

.emptyText {
  font-style: italic;
  padding: 8px;
  text-align: center;
  display: block;
}

.entityDescriptionFull,
.relationshipDescriptionFull {
  margin-top: 8px;
  padding: 8px;
  background: var(--background-light);
  border-radius: 4px;
}

/* 暗色主题下的新样式 */
[data-theme="dark"] .entityCard,
[data-theme="dark"] .relationshipCard {
  border-color: var(--border-color);
  background: var(--card-bg);
}

[data-theme="dark"] .entityCard.selected,
[data-theme="dark"] .relationshipCard.selected {
  background-color: rgba(24, 144, 255, 0.1);
}

[data-theme="dark"] .attributeItem,
[data-theme="dark"] .connectionItem {
  background: var(--code-bg);
}

[data-theme="dark"] .entityDescriptionFull,
[data-theme="dark"] .relationshipDescriptionFull {
  background: var(--code-bg);
}
