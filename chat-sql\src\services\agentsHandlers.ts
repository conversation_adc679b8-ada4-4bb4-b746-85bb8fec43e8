import { NextRequest, NextResponse } from "next/server";
import axios, { AxiosError } from "axios";
import {
  BailianAIRequest,
  BailianAIResponse,
  BailianAIError,
  BailianAIAPIError,
  ErrorType,
  HTTPStatus,
  DEFAULT_BAILIAN_CONFIG,
  DEFAULT_RETRY_CONFIG,
} from "@/types/chatBotTypes/bailianai";
import {
  SchemaGeneratorRequest,
  SchemaGeneratorResponse,
  AGENT_CONFIG,
} from "@/types/chatBotTypes/agents";

/**
 * 处理API错误
 */
export function handleAPIError(error: any): BailianAIAPIError {
  if (error instanceof AxiosError) {
    const status = error.response?.status;
    const data = error.response?.data as BailianAIError;

    switch (status) {
      case HTTPStatus.UNAUTHORIZED:
        return new BailianAIAPIError(
          "API密钥无效或已过期",
          ErrorType.AUTHENTICATION_ERROR,
          data?.code,
          data?.request_id,
          status,
        );
      case HTTPStatus.TOO_MANY_REQUESTS:
        return new BailianAIAPIError(
          "请求频率过高，请稍后重试",
          ErrorType.RATE_LIMIT_ERROR,
          data?.code,
          data?.request_id,
          status,
        );
      case HTTPStatus.BAD_REQUEST:
        return new BailianAIAPIError(
          data?.message || "请求参数错误",
          ErrorType.VALIDATION_ERROR,
          data?.code,
          data?.request_id,
          status,
        );
      default:
        return new BailianAIAPIError(
          data?.message || error.message || "网络请求失败",
          ErrorType.NETWORK_ERROR,
          data?.code,
          data?.request_id,
          status,
        );
    }
  }

  return new BailianAIAPIError(
    error.message || "未知错误",
    ErrorType.UNKNOWN_ERROR,
  );
}

/**
 * 实现指数退避重试
 */
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  config: typeof DEFAULT_RETRY_CONFIG = DEFAULT_RETRY_CONFIG,
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;

      // 如果是最后一次尝试，直接抛出错误
      if (attempt === config.maxRetries) {
        throw lastError;
      }

      // 如果是认证错误或验证错误，不进行重试
      if (error instanceof BailianAIAPIError) {
        if (
          error.type === ErrorType.AUTHENTICATION_ERROR ||
          error.type === ErrorType.VALIDATION_ERROR
        ) {
          throw error;
        }
      }

      // 计算延迟时间（指数退避）
      const delay = Math.min(
        config.baseDelay * Math.pow(config.backoffFactor, attempt),
        config.maxDelay,
      );

      console.log(`请求失败，${delay}ms后进行第${attempt + 1}次重试:`, error);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

/**
 * 通用的百炼AI API调用函数
 */
export async function callBailianAPI(
  request: BailianAIRequest,
  appId: string,
): Promise<BailianAIResponse> {
  const apiKey = process.env.DASHSCOPE_API_KEY;

  if (!apiKey) {
    throw new BailianAIAPIError(
      "API密钥未配置，请检查环境变量DASHSCOPE_API_KEY",
      ErrorType.AUTHENTICATION_ERROR,
    );
  }

  const url = `${DEFAULT_BAILIAN_CONFIG.baseUrl}/${appId}/completion`;

  const headers = {
    Authorization: `Bearer ${apiKey}`,
    "Content-Type": "application/json",
  };

  return retryWithBackoff(async () => {
    const response = await axios.post(url, request, {
      headers,
      timeout: DEFAULT_BAILIAN_CONFIG.timeout,
      responseType: "json",
    });

    return response.data;
  });
}

/**
 * 通用的请求创建函数模板
 */
export function createAgentRequest(
  prompt: string,
  bizParams: Record<string, any>,
  sessionId?: string,
  parameters?: any,
): BailianAIRequest {
  const request: BailianAIRequest = {
    input: {
      prompt: prompt,
      biz_params: bizParams,
    },
    parameters: parameters || {},
    debug: {},
  };

  if (sessionId) {
    request.input.session_id = sessionId;
  }

  return request;
}

/**
 * 通用的参数验证函数
 */
export function validateRequiredParams(
  params: Record<string, any>,
  requiredFields: string[],
): { isValid: boolean; missingField?: string } {
  for (const field of requiredFields) {
    if (!params[field] || typeof params[field] !== "string") {
      return { isValid: false, missingField: field };
    }
  }
  return { isValid: true };
}

/**
 * 通用的请求体解析函数
 */
export function parseRequestBody(
  body: any,
  expectedBizParams: string[],
): {
  bizParams: Record<string, any>;
  sessionId?: string;
  parameters?: any;
} {
  if (body.input && body.input.biz_params) {
    // 新格式：百炼AI标准格式
    const bizParams: Record<string, any> = {};
    for (const param of expectedBizParams) {
      bizParams[param] = body.input.biz_params[param];
    }

    return {
      bizParams,
      sessionId: body.input.session_id,
      parameters: body.parameters,
    };
  } else {
    throw new BailianAIAPIError(
      "请求参数格式错误，需要使用百炼AI标准格式",
      ErrorType.VALIDATION_ERROR,
    );
  }
}

/**
 * 通用的错误响应创建函数
 */
export function createErrorResponse<T>(
  error: any,
  ResponseType: new () => T,
): { response: T; statusCode: number } {
  const apiError = handleAPIError(error);
  const errorResponse = {
    success: false,
    error: {
      code: apiError.code || apiError.type,
      message: apiError.message,
    },
  } as T;

  const statusCode = apiError.statusCode || HTTPStatus.INTERNAL_SERVER_ERROR;
  return { response: errorResponse, statusCode };
}
