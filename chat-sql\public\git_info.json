{"history": [{"hash": "47232804f4b43c480026a697bfb1dc3ca829d768", "date": "2025-07-19T23:37:42+08:00", "message": "Merge branch 'new-feature'", "description": "", "author": "ffy6511"}, {"hash": "c19de1b7ba2f5e5e3f6caef2ff28d5f2788b4678", "date": "2025-07-19T23:29:22+08:00", "message": "feat: Track per-problem completion in tutorial progress", "description": "- Introduces a completedProblems array to LLMProblem records and updates progress logic to track individual problem completion.\n- Updates UI components and context to reflect granular progress, adds event-based refresh for history", "author": "ffy6511"}, {"hash": "21244c05aa41d84305cb424c597c7107a45de314", "date": "2025-07-19T22:49:04+08:00", "message": "feat: Add tutorial progress tracking and record clearing", "description": "- Implements tutorial progress tracking with status display, filtering, and automatic updates.\n- Adds a 'clear all records' feature with confirmation dialog in the history panel.\n- Refactors tutorial record saving to fix data issues and ensures TypeScript type safety.", "author": "ffy6511"}, {"hash": "8d0472672b7bd04d269cea06dab1435e8e17e593", "date": "2025-07-19T19:19:32+08:00", "message": "fix: B+ tree key handling and parent update logic", "description": "- Corrected key comparison in BPlusTreeCore search\n- Improved parent key update logic in BPlusTreeAlgorithm to handle edge cases when deleting keys.\n- Also removed redundant numKeys manipulations for consistency.", "author": "ffy6511"}, {"hash": "f26668dc3ca1bb892eb4e84768993525838246f1", "date": "2025-07-19T16:43:24+08:00", "message": "feat: Implement B+ tree node type management and persistent storage", "description": "- Added node type tracking in CommandExecutor to differentiate between leaf and internal nodes.\n- Introduced setNodeType method to set node type information.\n- Updated executeCreateBTreeNode to utilize node type information for node creation.", "author": "ffy6511"}, {"hash": "6d36b68934006fe2a71457c4ec3f435974cb72c4", "date": "2025-07-19T11:11:27+08:00", "message": "feat: Complete the scaffolding of the revised B + tree using the idea of instruction sequence", "description": "", "author": "ffy6511"}, {"hash": "f858a05158c8892d98fe364d471d0de9b600648a", "date": "2025-07-15T20:10:53+08:00", "message": "fix(nodeFactory): Added drag and drop recognition for weak entities", "description": "- Added support for initializing weak entities;\n- Modified the style of the model that opens the ER graph list", "author": "ffy6511"}, {"hash": "6537ffeb4ea42f75d70896813fd54af4e0a92826", "date": "2025-07-15T19:36:06+08:00", "message": "chore: Enhance ERDiagram drag, drop, and connect UX", "description": "- Centralizes drag-and-drop and connection logic into the ERDiagram component,\n- Updates context methods to async for persistence, improves handle visibility and interactivity in EntityNode and DiamondNode", "author": "ffy6511"}, {"hash": "483e551c639f72bff17547574120edd19e2c23ff", "date": "2025-07-15T17:52:59+08:00", "message": "fix(createNewDiagram): Enhance ER diagram UI and context logic", "description": "- Improved EntityListView to support dynamic data type parameters and better badge styling.\n- Refactored NewDiagramModal to use createNewDiagram for simplified logic.\n- Updated OpenDiagramModal to rely on context-managed diagram list", "author": "ffy6511"}, {"hash": "1c6db8992a36d57f1968e2ae5678af59d380d6b7", "date": "2025-07-15T15:55:09+08:00", "message": "refactor  Inspector and Views", "description": "- Moved EntitiesView and RelationshipsView to separate components\n- Updated Inspector component to use new views.", "author": "ffy6511"}, {"hash": "12f7a818220388b28a9dc1fc5453fb25b177f196", "date": "2025-07-14T17:14:47+08:00", "message": "refactor(Inspector): use MUI component", "description": "- Replaces Ant Design components with Material UI in the ERDiagram Inspector and related UI\n-  Also updates global and sidebar CSS variables, adjusts NavBar tab styles, and tweaks loading delay in layout.", "author": "ffy6511"}, {"hash": "49275eb621b4749fa049045667ad2e41533a7574", "date": "2025-07-14T15:23:12+08:00", "message": "feat(PropertyEditor): Add ER graph node editing and indexDB storage", "description": "- Implement ER graph node editing, weak entity set double border rendering, double-click renaming, node selection state management, right property editing panel and other functions;\n- Improve IndexedDB storage service\n- Optimize left sidebar width and global style", "author": "ffy6511"}, {"hash": "73f5ddba1f5bc284ad40e016a620627273e7f281", "date": "2025-07-14T11:29:27+08:00", "message": "feat(weak type): Add weak entity and weak relationship rendering support", "description": "- Implemented support for weak entities and weak relationships  in ER diagrams.\n- Added a new weak entity test page and sample data, updated ERDiagram types, node components, and styles to visually distinguish weak entities and relationships.", "author": "ffy6511"}, {"hash": "5c65187f17798bb57e8740d274d6cfbbd86a6607", "date": "2025-07-14T00:17:41+08:00", "message": "feat: Add drag-and-drop to ER diagram canvas and context actions", "description": "- Implemented drag-and-drop support for adding entities and relationships from the component library to the ER diagram canvas.\n- Added utility functions for node creation and drop position calculation.\n- Extended ERDiagramContext with add, update, and delete actions for entities and relationships.", "author": "ffy6511"}, {"hash": "002fb58d90c822f7b2a566e1c7cba807f5056093", "date": "2025-07-14T00:01:41+08:00", "message": "feat(er-diagram): scaffold ER diagram module with basic layout and components", "description": "Introduces a new ER diagram module with interactive visual modeling, including <PERSON><PERSON>, <PERSON><PERSON>, Inspector, and context-based state management.", "author": "ffy6511"}, {"hash": "348af836f800f89f24905cee0ec5ffdf707871f7", "date": "2025-07-13T20:59:49+08:00", "message": "refactor(ERDiagram): adjusted cardinality and layout logic", "description": "- Standardized cardinality notation in ER types and data to (min..max) format, removed isForeignKey\n- Refactored erToFlow layout to use a fixed 3-column approach and simplified edge handle assignment based on entity position.\n- DiamondNode now supports both source and target handles on all sides for improved connection flexibility.", "author": "ffy6511"}, {"hash": "157f7fc76e287fcb7fb8739c659ba911c7635aa9", "date": "2025-07-12T13:39:06+08:00", "message": "feat(ERDiagram): Add ER diagram visualization components", "description": "- Introduced a new ER diagram visualization feature with custom React Flow nodes and edges for entities and relationships.\n- Added demo page, reusable ERDiagram component, custom node styles (entity and diamond), edge types, and sample ER data.", "author": "ffy6511"}, {"hash": "1e24706891b6b3ebdda65b2e2f50f9e9fc15de00", "date": "2025-07-12T08:22:54+08:00", "message": "feat: Add adjustable B+ tree order and animation", "description": "- The visualizer layout is refactored.\n- The B+ tree logic is refactored to support dynamic order changes, improved animation step sequencing", "author": "ffy6511"}, {"hash": "a14db290f2a85bda53728a0d5cdf8f219c283f02", "date": "2025-07-10T16:05:44+08:00", "message": "style(BPlusTreeVisualizer): Enhance B+ tree visualizer with custom controls and UI improvements", "description": "- Added custom zoom and reset controls to the B+ tree visualizer using a new horizontal button panel.\n- Improved node layout logic for better alignment.\n- Error messages now use Ant Design's message component for better user feedback.", "author": "ffy6511"}, {"hash": "a514013c8402cf9346600a8ba5d072ada49c6640", "date": "2025-07-09T22:47:30+08:00", "message": "feat: add SettingsPanel component for animation settings and remove SimpleDemo component", "description": "- Introduced a new SettingsPanel component to manage animation settings including enabling/disabling animations and adjusting animation speed.\n- Removed the SimpleDemo component which was previously handling B+ tree visualization.\n- Updated utility functions to accommodate changes in B+ tree structure and added order property to node data.\n- Implemented a comprehensive B+ tree class with methods f...", "author": "ffy6511"}, {"hash": "6b49b5114177192926642fb899b8fbfd4897adda", "date": "2025-07-09T15:59:13+08:00", "message": "refactor : B+ tree visualizer node layout and styles", "description": "- Simplified and unified the slot-based layout for both internal and leaf B+ tree nodes\n- Updated CSS to use slot containers and modernized node appearance", "author": "ffy6511"}, {"hash": "25d7283ca00930efaf9a0ff4ba15c7930cd8e923", "date": "2025-07-09T14:47:14+08:00", "message": "add(BPlusXyflow): Complete scaffolding file for B + tree visualization", "description": "", "author": "ffy6511"}, {"hash": "2953989274c234e462fa29d731f08666bd3c8c57", "date": "2025-05-25T22:19:59+08:00", "message": "feat(themeToggle): Enhance UI components with theme support and improved styling", "description": "- Updated TableNavigator to include a search input with an icon and custom styles.\n- Added TupleViewer.module.css for styling the TupleViewer component.\n- Modified TupleViewer to use new styles and improved background color.\n- Refactored NavBar.module.css to use CSS variables for background and border colors.\n- Updated SideBar.module.css to apply theme variables for consistent styling.\n- Introduce...", "author": "ffy6511"}, {"hash": "00b51ecaf34af5689d8019d898b4ba67aec3a6f1", "date": "2025-05-16T00:04:25+08:00", "message": "feat(TableNavigator & share):  A table navigation  to quickly locate tables; new share for File-mediated database interaction patterns", "description": "- Added TableNavigator component to provide the function of searching and locating tables;\n- Added the sharing function of sharing database resources by exporting and importing files", "author": "ffy6511"}, {"hash": "d8ac28495465c114e1307854f9837addf167d4d8", "date": "2025-05-13T00:27:42+08:00", "message": "feat(DatabaseFlow): Enhance constraint icon and prompt box style, optimize foreign key display", "description": "- Add constraint icons and prompt boxes to provide details for primary and foreign keys\n- Update foreign key reference information to ensure table names are displayed correctly\n- Modify styles to improve user experience and ensure prompt boxes are displayed above nodes\n- Update database type definitions to support new foreign key structures", "author": "ffy6511"}, {"hash": "bcf12e6e850998e5097f10f246fac782396e7766", "date": "2025-05-11T10:52:58+08:00", "message": "feat(loading&Databaseflow): Skeleton diagram effect , Better schema foreign key display, Supplement the schema of the textbook.", "description": "- Use the Skeleton component of MUI to design animation effects of loading state\n- Add style selection of edges with foreign key constraints to provide more appropriate fold lines for complex schemas\n- The schema of the implementation textbook imported from the database system\n- Handle implicit join (Cartesian product)", "author": "ffy6511"}, {"hash": "31ae25ca709d6a363bfb5b082ae0deca20215248", "date": "2025-05-10T15:52:59+08:00", "message": "style: Improve the display of schema and changelog page", "description": " - improve the style of database files, set the appropriate animation time for scaling transformations\n - improve the card display of changelog (add detailed description)", "author": "ffy6511"}, {"hash": "4cbd89a5614825a8f7848a37bb681cbb1f230330", "date": "2025-05-10T14:18:39+08:00", "message": "feat(sqlCompletionProvider):  Improve the complete settings: .Define relevant keywords according to the context.", "description": "- Use sortText to control the sorting of suggestion items, - Separate the floating prompt into a component for easy maintenance separately from the completion prompt\n- Avoid typing meaningless spaces to trigger the completion prompt", "author": "ffy6511"}, {"hash": "683646c7d5283f3de35834cd4f66d3d8a8ad48b7", "date": "2025-05-08T17:20:04+08:00", "message": "docs(README)", "description": "- Modify the readme documentation according to the updated content\n- Added a link to deepwiki", "author": "ffy6511"}, {"hash": "777e771ac6fea46a9ec7f3fa6d2988f311b2e758", "date": "2025-05-08T15:45:00+08:00", "message": "feat: Support for having and limiting queries; enhancement of floating hints", "description": "- support for limit and offset functions, limiting the number of queries\n- (evaluateExpression) fix queries where simple conditions\n- handle having conditions\n- modify suspended prompts to English, while wrapping according to the md format of the momaco editor,\n - modify prompt rules, support multiple word prompts", "author": "ffy6511"}, {"hash": "4e138c7a6ea96f9e33560cfec99568a8332acb24", "date": "2025-05-08T15:01:37+08:00", "message": "feat(sqlExecutor): Support for processing nested subqueries in from and where", "description": "- Processing nested subqueries: Extract the actual subquery AST from subqueryClause.expr.ast, using the subquery results as the data source for external queries\n- Support processing of nested subqueries in from and where\n - Special handling is required for join clauses containing subqueries", "author": "ffy6511"}, {"hash": "21917f18b01cc348c7e91b74b583c59962101440", "date": "2025-05-08T14:33:56+08:00", "message": "fix(handleNaturalJoin):  Correct handling of natural join", "description": "- Since node-sql-parser cannot handle natural joins directly, retrieve the sql statement, manually modify the on property in ast if there is a natural join, and then continue execution as an inner join.\n- Update the return interface in the type", "author": "ffy6511"}, {"hash": "ebf20dbb4f6858abdd71129e026c96ced7bb610a", "date": "2025-05-08T14:13:23+08:00", "message": "fix(queryHelpers): Group and order operations", "description": "- Fixed an issue with group by when there is an alias\n - Fixed an issue where order by could not recognize the alias", "author": "ffy6511"}, {"hash": "9c3a6c36f06795cb63485bb3ae8a60b00f961e5a", "date": "2025-05-06T23:43:41+08:00", "message": "feat(queryHelpers):  Support for basic join operations", "description": "- Supports basic join operations; ensures that the schema's table is dragged and dropped at the highest level.\n- Displays null values instead of rendering them as \"null\" in the resulting table.", "author": "ffy6511"}, {"hash": "8cc90e9a918f1fbb152147a77199b827aed33a7d", "date": "2025-04-30T21:42:29+08:00", "message": "feat(sqlEditor): Style adjustment and error handling of code editing areas", "description": "- Ensure that the query results are also cleared when the SQL statement is illegal;\n- Added animated prompts for correct comparisons;\n- Temporarily cleared redundant functions such as things execution, and adjusted the position of buttons.", "author": "ffy6511"}, {"hash": "687dce225767bbb4048aefd21eb42baeb6f082a8", "date": "2025-04-30T20:55:21+08:00", "message": "fix(sqlExecutor & sqlEditor): Added aggregate function support for the SQL execution engine", "description": "- Optimized the prompt when there is a syntax error in the query statement;\n- Optimized the style when the query result is empty\n- Avoided the repeated rendering of the schema during code editing", "author": "ffy6511"}, {"hash": "27bd4f8d16d21a42ea3e3fd7a9dd51d075b6c8de", "date": "2025-04-30T15:19:22+08:00", "message": "feat(ShareLink): Add ShareButton component for sharing database links", "description": "- Enhance date formatting with error handling\n-  Implement data import from shared links", "author": "ffy6511"}, {"hash": "f436be9ab7d5adcb34639bcb052e2fb9636b3222", "date": "2025-04-30T15:05:49+08:00", "message": "feat(Tutorial): Added pre-basic exercises", "description": "- Sidebar has added the function of initialization tutorial;\n- Improved the style of history entries, and added tags for distinction.", "author": "ffy6511"}, {"hash": "6e759cd7b42828386edf53934dd5331363697ece", "date": "2025-04-30T11:49:09+08:00", "message": "fix(changeLog): Add the ability to generate git information and update related interfaces", "description": "", "author": "ffy6511"}, {"hash": "e6349aa63e0a3d3b26648f9fc964580379a0b5a0", "date": "2025-04-30T00:01:05+08:00", "message": "docs(GuidingModal):", "description": "- Modified the deployment instructions in the README.\n- Added the content of the help module. Temporarily removed the log page.", "author": "ffy6511"}, {"hash": "e037da525ab951f0909986723e759463b45855f9", "date": "2025-04-29T23:08:35+08:00", "message": "feat(navBar):changelog page & Sidebar updated", "description": "- Added a changelog page to display recent commit records\n- Feature update for the sidebar.", "author": "ffy6511"}, {"hash": "dec792d0f1df30e7822bd4b61511c03c350b269e", "date": "2025-04-28T23:52:36+08:00", "message": "feat(navBar): Added logo and navigation bar design.", "description": "", "author": "ffy6511"}, {"hash": "5466abea80ad3441708590d0dc92b8be2d416c3d", "date": "2025-04-28T21:41:55+08:00", "message": "fix(README)", "description": "", "author": "ffy6511"}, {"hash": "0fd371d8d18463c4066c1bca87fc4fa8488a888a", "date": "2025-04-28T21:17:02+08:00", "message": "docs(README)", "description": "- explaining the technology stack used by the projectthe\n- main features\n-  method of local deployment.", "author": "ffy6511"}, {"hash": "b04b8bcf00c819defa2283b50df4d1fcd12ed0ca", "date": "2025-04-28T19:04:33+08:00", "message": "style: Major modifications to the overall style.", "description": "", "author": "ffy6511"}, {"hash": "eee3382a80b1a921a1363b5fe5a9f59b5eb26215", "date": "2025-04-27T23:32:20+08:00", "message": "fix: Update ESLint configuration to disable specific rules, optimize style and component logic", "description": "", "author": "ffy6511"}, {"hash": "1cf9720893ad425830e8df8c2fb5d59b0170ff15", "date": "2025-04-27T22:37:50+08:00", "message": "feat(HistoryPanel): Added search function of search bar and corresponding shortcut", "description": "", "author": "ffy6511"}, {"hash": "0a3021c7b5ff3269162cfffc07e3db2b13c27e23", "date": "2025-04-27T21:04:33+08:00", "message": "fix(problem<PERSON>iewer):Update answer status when switching questions", "description": "- Added management context for switching scenarios such as questions Clear the input area and render area.", "author": "ffy6511"}, {"hash": "52af636814eed34ac333df281cef950ccb705937", "date": "2025-04-27T20:41:18+08:00", "message": "Style: Styling schema and tuple data.", "description": "- Added edges for foreign key constraints;\n- Adjusted the display of tuple properties", "author": "ffy6511"}, {"hash": "05b57412e81b7902138cd6fec54cdc4d90d55d8b", "date": "2025-04-27T14:45:36+08:00", "message": "feat(LLMWindow): Modify the layout format and logic of the LLM window", "description": "", "author": "ffy6511"}], "commitsByDate": {"2025/7/19": [{"hash": "47232804f4b43c480026a697bfb1dc3ca829d768", "date": "2025-07-19T23:37:42+08:00", "message": "Merge branch 'new-feature'", "description": "", "author": "ffy6511"}, {"hash": "c19de1b7ba2f5e5e3f6caef2ff28d5f2788b4678", "date": "2025-07-19T23:29:22+08:00", "message": "feat: Track per-problem completion in tutorial progress", "description": "- Introduces a completedProblems array to LLMProblem records and updates progress logic to track individual problem completion.\n- Updates UI components and context to reflect granular progress, adds event-based refresh for history", "author": "ffy6511"}, {"hash": "21244c05aa41d84305cb424c597c7107a45de314", "date": "2025-07-19T22:49:04+08:00", "message": "feat: Add tutorial progress tracking and record clearing", "description": "- Implements tutorial progress tracking with status display, filtering, and automatic updates.\n- Adds a 'clear all records' feature with confirmation dialog in the history panel.\n- Refactors tutorial record saving to fix data issues and ensures TypeScript type safety.", "author": "ffy6511"}, {"hash": "8d0472672b7bd04d269cea06dab1435e8e17e593", "date": "2025-07-19T19:19:32+08:00", "message": "fix: B+ tree key handling and parent update logic", "description": "- Corrected key comparison in BPlusTreeCore search\n- Improved parent key update logic in BPlusTreeAlgorithm to handle edge cases when deleting keys.\n- Also removed redundant numKeys manipulations for consistency.", "author": "ffy6511"}, {"hash": "f26668dc3ca1bb892eb4e84768993525838246f1", "date": "2025-07-19T16:43:24+08:00", "message": "feat: Implement B+ tree node type management and persistent storage", "description": "- Added node type tracking in CommandExecutor to differentiate between leaf and internal nodes.\n- Introduced setNodeType method to set node type information.\n- Updated executeCreateBTreeNode to utilize node type information for node creation.", "author": "ffy6511"}, {"hash": "6d36b68934006fe2a71457c4ec3f435974cb72c4", "date": "2025-07-19T11:11:27+08:00", "message": "feat: Complete the scaffolding of the revised B + tree using the idea of instruction sequence", "description": "", "author": "ffy6511"}], "2025/7/15": [{"hash": "f858a05158c8892d98fe364d471d0de9b600648a", "date": "2025-07-15T20:10:53+08:00", "message": "fix(nodeFactory): Added drag and drop recognition for weak entities", "description": "- Added support for initializing weak entities;\n- Modified the style of the model that opens the ER graph list", "author": "ffy6511"}, {"hash": "6537ffeb4ea42f75d70896813fd54af4e0a92826", "date": "2025-07-15T19:36:06+08:00", "message": "chore: Enhance ERDiagram drag, drop, and connect UX", "description": "- Centralizes drag-and-drop and connection logic into the ERDiagram component,\n- Updates context methods to async for persistence, improves handle visibility and interactivity in EntityNode and DiamondNode", "author": "ffy6511"}, {"hash": "483e551c639f72bff17547574120edd19e2c23ff", "date": "2025-07-15T17:52:59+08:00", "message": "fix(createNewDiagram): Enhance ER diagram UI and context logic", "description": "- Improved EntityListView to support dynamic data type parameters and better badge styling.\n- Refactored NewDiagramModal to use createNewDiagram for simplified logic.\n- Updated OpenDiagramModal to rely on context-managed diagram list", "author": "ffy6511"}, {"hash": "1c6db8992a36d57f1968e2ae5678af59d380d6b7", "date": "2025-07-15T15:55:09+08:00", "message": "refactor  Inspector and Views", "description": "- Moved EntitiesView and RelationshipsView to separate components\n- Updated Inspector component to use new views.", "author": "ffy6511"}], "2025/7/14": [{"hash": "12f7a818220388b28a9dc1fc5453fb25b177f196", "date": "2025-07-14T17:14:47+08:00", "message": "refactor(Inspector): use MUI component", "description": "- Replaces Ant Design components with Material UI in the ERDiagram Inspector and related UI\n-  Also updates global and sidebar CSS variables, adjusts NavBar tab styles, and tweaks loading delay in layout.", "author": "ffy6511"}, {"hash": "49275eb621b4749fa049045667ad2e41533a7574", "date": "2025-07-14T15:23:12+08:00", "message": "feat(PropertyEditor): Add ER graph node editing and indexDB storage", "description": "- Implement ER graph node editing, weak entity set double border rendering, double-click renaming, node selection state management, right property editing panel and other functions;\n- Improve IndexedDB storage service\n- Optimize left sidebar width and global style", "author": "ffy6511"}, {"hash": "73f5ddba1f5bc284ad40e016a620627273e7f281", "date": "2025-07-14T11:29:27+08:00", "message": "feat(weak type): Add weak entity and weak relationship rendering support", "description": "- Implemented support for weak entities and weak relationships  in ER diagrams.\n- Added a new weak entity test page and sample data, updated ERDiagram types, node components, and styles to visually distinguish weak entities and relationships.", "author": "ffy6511"}, {"hash": "5c65187f17798bb57e8740d274d6cfbbd86a6607", "date": "2025-07-14T00:17:41+08:00", "message": "feat: Add drag-and-drop to ER diagram canvas and context actions", "description": "- Implemented drag-and-drop support for adding entities and relationships from the component library to the ER diagram canvas.\n- Added utility functions for node creation and drop position calculation.\n- Extended ERDiagramContext with add, update, and delete actions for entities and relationships.", "author": "ffy6511"}, {"hash": "002fb58d90c822f7b2a566e1c7cba807f5056093", "date": "2025-07-14T00:01:41+08:00", "message": "feat(er-diagram): scaffold ER diagram module with basic layout and components", "description": "Introduces a new ER diagram module with interactive visual modeling, including <PERSON><PERSON>, <PERSON><PERSON>, Inspector, and context-based state management.", "author": "ffy6511"}], "2025/7/13": [{"hash": "348af836f800f89f24905cee0ec5ffdf707871f7", "date": "2025-07-13T20:59:49+08:00", "message": "refactor(ERDiagram): adjusted cardinality and layout logic", "description": "- Standardized cardinality notation in ER types and data to (min..max) format, removed isForeignKey\n- Refactored erToFlow layout to use a fixed 3-column approach and simplified edge handle assignment based on entity position.\n- DiamondNode now supports both source and target handles on all sides for improved connection flexibility.", "author": "ffy6511"}], "2025/7/12": [{"hash": "157f7fc76e287fcb7fb8739c659ba911c7635aa9", "date": "2025-07-12T13:39:06+08:00", "message": "feat(ERDiagram): Add ER diagram visualization components", "description": "- Introduced a new ER diagram visualization feature with custom React Flow nodes and edges for entities and relationships.\n- Added demo page, reusable ERDiagram component, custom node styles (entity and diamond), edge types, and sample ER data.", "author": "ffy6511"}, {"hash": "1e24706891b6b3ebdda65b2e2f50f9e9fc15de00", "date": "2025-07-12T08:22:54+08:00", "message": "feat: Add adjustable B+ tree order and animation", "description": "- The visualizer layout is refactored.\n- The B+ tree logic is refactored to support dynamic order changes, improved animation step sequencing", "author": "ffy6511"}], "2025/7/10": [{"hash": "a14db290f2a85bda53728a0d5cdf8f219c283f02", "date": "2025-07-10T16:05:44+08:00", "message": "style(BPlusTreeVisualizer): Enhance B+ tree visualizer with custom controls and UI improvements", "description": "- Added custom zoom and reset controls to the B+ tree visualizer using a new horizontal button panel.\n- Improved node layout logic for better alignment.\n- Error messages now use Ant Design's message component for better user feedback.", "author": "ffy6511"}], "2025/7/9": [{"hash": "a514013c8402cf9346600a8ba5d072ada49c6640", "date": "2025-07-09T22:47:30+08:00", "message": "feat: add SettingsPanel component for animation settings and remove SimpleDemo component", "description": "- Introduced a new SettingsPanel component to manage animation settings including enabling/disabling animations and adjusting animation speed.\n- Removed the SimpleDemo component which was previously handling B+ tree visualization.\n- Updated utility functions to accommodate changes in B+ tree structure and added order property to node data.\n- Implemented a comprehensive B+ tree class with methods f...", "author": "ffy6511"}, {"hash": "6b49b5114177192926642fb899b8fbfd4897adda", "date": "2025-07-09T15:59:13+08:00", "message": "refactor : B+ tree visualizer node layout and styles", "description": "- Simplified and unified the slot-based layout for both internal and leaf B+ tree nodes\n- Updated CSS to use slot containers and modernized node appearance", "author": "ffy6511"}, {"hash": "25d7283ca00930efaf9a0ff4ba15c7930cd8e923", "date": "2025-07-09T14:47:14+08:00", "message": "add(BPlusXyflow): Complete scaffolding file for B + tree visualization", "description": "", "author": "ffy6511"}], "2025/5/25": [{"hash": "2953989274c234e462fa29d731f08666bd3c8c57", "date": "2025-05-25T22:19:59+08:00", "message": "feat(themeToggle): Enhance UI components with theme support and improved styling", "description": "- Updated TableNavigator to include a search input with an icon and custom styles.\n- Added TupleViewer.module.css for styling the TupleViewer component.\n- Modified TupleViewer to use new styles and improved background color.\n- Refactored NavBar.module.css to use CSS variables for background and border colors.\n- Updated SideBar.module.css to apply theme variables for consistent styling.\n- Introduce...", "author": "ffy6511"}], "2025/5/16": [{"hash": "00b51ecaf34af5689d8019d898b4ba67aec3a6f1", "date": "2025-05-16T00:04:25+08:00", "message": "feat(TableNavigator & share):  A table navigation  to quickly locate tables; new share for File-mediated database interaction patterns", "description": "- Added TableNavigator component to provide the function of searching and locating tables;\n- Added the sharing function of sharing database resources by exporting and importing files", "author": "ffy6511"}], "2025/5/13": [{"hash": "d8ac28495465c114e1307854f9837addf167d4d8", "date": "2025-05-13T00:27:42+08:00", "message": "feat(DatabaseFlow): Enhance constraint icon and prompt box style, optimize foreign key display", "description": "- Add constraint icons and prompt boxes to provide details for primary and foreign keys\n- Update foreign key reference information to ensure table names are displayed correctly\n- Modify styles to improve user experience and ensure prompt boxes are displayed above nodes\n- Update database type definitions to support new foreign key structures", "author": "ffy6511"}], "2025/5/11": [{"hash": "bcf12e6e850998e5097f10f246fac782396e7766", "date": "2025-05-11T10:52:58+08:00", "message": "feat(loading&Databaseflow): Skeleton diagram effect , Better schema foreign key display, Supplement the schema of the textbook.", "description": "- Use the Skeleton component of MUI to design animation effects of loading state\n- Add style selection of edges with foreign key constraints to provide more appropriate fold lines for complex schemas\n- The schema of the implementation textbook imported from the database system\n- Handle implicit join (Cartesian product)", "author": "ffy6511"}], "2025/5/10": [{"hash": "31ae25ca709d6a363bfb5b082ae0deca20215248", "date": "2025-05-10T15:52:59+08:00", "message": "style: Improve the display of schema and changelog page", "description": " - improve the style of database files, set the appropriate animation time for scaling transformations\n - improve the card display of changelog (add detailed description)", "author": "ffy6511"}, {"hash": "4cbd89a5614825a8f7848a37bb681cbb1f230330", "date": "2025-05-10T14:18:39+08:00", "message": "feat(sqlCompletionProvider):  Improve the complete settings: .Define relevant keywords according to the context.", "description": "- Use sortText to control the sorting of suggestion items, - Separate the floating prompt into a component for easy maintenance separately from the completion prompt\n- Avoid typing meaningless spaces to trigger the completion prompt", "author": "ffy6511"}], "2025/5/8": [{"hash": "683646c7d5283f3de35834cd4f66d3d8a8ad48b7", "date": "2025-05-08T17:20:04+08:00", "message": "docs(README)", "description": "- Modify the readme documentation according to the updated content\n- Added a link to deepwiki", "author": "ffy6511"}, {"hash": "777e771ac6fea46a9ec7f3fa6d2988f311b2e758", "date": "2025-05-08T15:45:00+08:00", "message": "feat: Support for having and limiting queries; enhancement of floating hints", "description": "- support for limit and offset functions, limiting the number of queries\n- (evaluateExpression) fix queries where simple conditions\n- handle having conditions\n- modify suspended prompts to English, while wrapping according to the md format of the momaco editor,\n - modify prompt rules, support multiple word prompts", "author": "ffy6511"}, {"hash": "4e138c7a6ea96f9e33560cfec99568a8332acb24", "date": "2025-05-08T15:01:37+08:00", "message": "feat(sqlExecutor): Support for processing nested subqueries in from and where", "description": "- Processing nested subqueries: Extract the actual subquery AST from subqueryClause.expr.ast, using the subquery results as the data source for external queries\n- Support processing of nested subqueries in from and where\n - Special handling is required for join clauses containing subqueries", "author": "ffy6511"}, {"hash": "21917f18b01cc348c7e91b74b583c59962101440", "date": "2025-05-08T14:33:56+08:00", "message": "fix(handleNaturalJoin):  Correct handling of natural join", "description": "- Since node-sql-parser cannot handle natural joins directly, retrieve the sql statement, manually modify the on property in ast if there is a natural join, and then continue execution as an inner join.\n- Update the return interface in the type", "author": "ffy6511"}, {"hash": "ebf20dbb4f6858abdd71129e026c96ced7bb610a", "date": "2025-05-08T14:13:23+08:00", "message": "fix(queryHelpers): Group and order operations", "description": "- Fixed an issue with group by when there is an alias\n - Fixed an issue where order by could not recognize the alias", "author": "ffy6511"}], "2025/5/6": [{"hash": "9c3a6c36f06795cb63485bb3ae8a60b00f961e5a", "date": "2025-05-06T23:43:41+08:00", "message": "feat(queryHelpers):  Support for basic join operations", "description": "- Supports basic join operations; ensures that the schema's table is dragged and dropped at the highest level.\n- Displays null values instead of rendering them as \"null\" in the resulting table.", "author": "ffy6511"}], "2025/4/30": [{"hash": "8cc90e9a918f1fbb152147a77199b827aed33a7d", "date": "2025-04-30T21:42:29+08:00", "message": "feat(sqlEditor): Style adjustment and error handling of code editing areas", "description": "- Ensure that the query results are also cleared when the SQL statement is illegal;\n- Added animated prompts for correct comparisons;\n- Temporarily cleared redundant functions such as things execution, and adjusted the position of buttons.", "author": "ffy6511"}, {"hash": "687dce225767bbb4048aefd21eb42baeb6f082a8", "date": "2025-04-30T20:55:21+08:00", "message": "fix(sqlExecutor & sqlEditor): Added aggregate function support for the SQL execution engine", "description": "- Optimized the prompt when there is a syntax error in the query statement;\n- Optimized the style when the query result is empty\n- Avoided the repeated rendering of the schema during code editing", "author": "ffy6511"}, {"hash": "27bd4f8d16d21a42ea3e3fd7a9dd51d075b6c8de", "date": "2025-04-30T15:19:22+08:00", "message": "feat(ShareLink): Add ShareButton component for sharing database links", "description": "- Enhance date formatting with error handling\n-  Implement data import from shared links", "author": "ffy6511"}, {"hash": "f436be9ab7d5adcb34639bcb052e2fb9636b3222", "date": "2025-04-30T15:05:49+08:00", "message": "feat(Tutorial): Added pre-basic exercises", "description": "- Sidebar has added the function of initialization tutorial;\n- Improved the style of history entries, and added tags for distinction.", "author": "ffy6511"}, {"hash": "6e759cd7b42828386edf53934dd5331363697ece", "date": "2025-04-30T11:49:09+08:00", "message": "fix(changeLog): Add the ability to generate git information and update related interfaces", "description": "", "author": "ffy6511"}, {"hash": "e6349aa63e0a3d3b26648f9fc964580379a0b5a0", "date": "2025-04-30T00:01:05+08:00", "message": "docs(GuidingModal):", "description": "- Modified the deployment instructions in the README.\n- Added the content of the help module. Temporarily removed the log page.", "author": "ffy6511"}], "2025/4/29": [{"hash": "e037da525ab951f0909986723e759463b45855f9", "date": "2025-04-29T23:08:35+08:00", "message": "feat(navBar):changelog page & Sidebar updated", "description": "- Added a changelog page to display recent commit records\n- Feature update for the sidebar.", "author": "ffy6511"}], "2025/4/28": [{"hash": "dec792d0f1df30e7822bd4b61511c03c350b269e", "date": "2025-04-28T23:52:36+08:00", "message": "feat(navBar): Added logo and navigation bar design.", "description": "", "author": "ffy6511"}, {"hash": "5466abea80ad3441708590d0dc92b8be2d416c3d", "date": "2025-04-28T21:41:55+08:00", "message": "fix(README)", "description": "", "author": "ffy6511"}, {"hash": "0fd371d8d18463c4066c1bca87fc4fa8488a888a", "date": "2025-04-28T21:17:02+08:00", "message": "docs(README)", "description": "- explaining the technology stack used by the projectthe\n- main features\n-  method of local deployment.", "author": "ffy6511"}, {"hash": "b04b8bcf00c819defa2283b50df4d1fcd12ed0ca", "date": "2025-04-28T19:04:33+08:00", "message": "style: Major modifications to the overall style.", "description": "", "author": "ffy6511"}], "2025/4/27": [{"hash": "eee3382a80b1a921a1363b5fe5a9f59b5eb26215", "date": "2025-04-27T23:32:20+08:00", "message": "fix: Update ESLint configuration to disable specific rules, optimize style and component logic", "description": "", "author": "ffy6511"}, {"hash": "1cf9720893ad425830e8df8c2fb5d59b0170ff15", "date": "2025-04-27T22:37:50+08:00", "message": "feat(HistoryPanel): Added search function of search bar and corresponding shortcut", "description": "", "author": "ffy6511"}, {"hash": "0a3021c7b5ff3269162cfffc07e3db2b13c27e23", "date": "2025-04-27T21:04:33+08:00", "message": "fix(problem<PERSON>iewer):Update answer status when switching questions", "description": "- Added management context for switching scenarios such as questions Clear the input area and render area.", "author": "ffy6511"}, {"hash": "52af636814eed34ac333df281cef950ccb705937", "date": "2025-04-27T20:41:18+08:00", "message": "Style: Styling schema and tuple data.", "description": "- Added edges for foreign key constraints;\n- Adjusted the display of tuple properties", "author": "ffy6511"}, {"hash": "05b57412e81b7902138cd6fec54cdc4d90d55d8b", "date": "2025-04-27T14:45:36+08:00", "message": "feat(LLMWindow): Modify the layout format and logic of the LLM window", "description": "", "author": "ffy6511"}]}, "stats": {"totalCommits": 50, "contributors": ["ffy6511"], "lastUpdate": "2025-07-19T23:37:42+08:00", "activeBranches": 6}}